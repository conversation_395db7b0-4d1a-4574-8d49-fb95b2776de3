<?php
get_header();
?>
<div class="hero" style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');">
    <div class="hero-container-program">
        <h1 class="hero-heading-program "><?php the_title(); ?></h1>
    </div>
</div>
<div class="content-container-program">
    <?php
    if (have_posts()) :
        while (have_posts()) : the_post(); ?>
            <div class="smort-content page-content">
                <?php the_content(); ?>
            </div>
    <?php endwhile;
    else :
        echo '<p>No content found.</p>';
    endif;
    ?>
</div>
<div class="social-media-share-container">
    <p> Ta ställning och dela artikeln </p>
    <div class="social-media-share">
        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" target="_blank" class="social-media-share-icon">
            <img src="<?php echo get_stylesheet_directory_uri(); ?>/img/facebook-icon.svg" alt="Dela på Facebook" />
        </a>
        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" target="_blank" class="social-media-share-icon">
            <img src="<?php echo get_stylesheet_directory_uri(); ?>/img/x-icon.svg" alt="Dela på X" />
        </a>
        <a href="https://www.linkedin.com/shareArticle?mini=true&url=<?php echo urlencode(get_permalink()); ?>&title=<?php echo urlencode(get_the_title()); ?>" target="_blank" class="social-media-share-icon">
            <img src="<?php echo get_stylesheet_directory_uri(); ?>/img/linkedin.svg" alt="Dela på LinkedIn" />
        </a>
        <a href="mailto:?subject=<?php echo rawurlencode(get_the_title()); ?>&body=<?php echo rawurlencode(get_permalink()); ?>" class="social-media-share-icon">
            <img src="<?php echo get_stylesheet_directory_uri(); ?>/img/mail-icon.svg" alt="Dela via mail" />
        </a>
    </div>
</div>
<div class="content-container-relatad-program">
    <h2 class="related-title"> Läs mer om våra program </h1>
        <?php
        $current_post_id = get_the_ID();
        $args = array(
            'post_type' => 'program',
            'posts_per_page' => 3,
            'post__not_in' => array($current_post_id),
            'orderby' => 'DESC',
        );
        $related_posts = new WP_Query($args);
        if ($related_posts->have_posts()) : ?>
            <div class="program-list">
                <?php while ($related_posts->have_posts()) : $related_posts->the_post(); ?>
                    <div class="program-item">
                        <a href="<?php the_permalink(); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="program-thumbnail">
                                    <?php the_post_thumbnail('large'); ?>
                                </div>
                            <?php endif; ?>
                            <div class="program-info-div">
                                <h2 class="program-title"><?php the_title(); ?></h2>
                                <a class="las-mer-program" href="<?php the_permalink(); ?>">Läs mer</a>
                            </div>
                        </a>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else : ?>
            <p>No related programs found.</p>
        <?php endif;
        wp_reset_postdata();
        ?>
</div>

<?php
get_footer();
?>