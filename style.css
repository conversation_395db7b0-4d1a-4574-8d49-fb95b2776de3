/*
 Theme Name:   Moderaterna - Mark
 Theme URI:    https://smort.se
 Description:  Smort Commerce Child Theme
 Author:       Smort AB
 Author URI:   https://smort.se
 Template:     moderaterna
 Version:      1.0.0
*/

/* Import the parent theme's stylesheet */

/* Add your custom styles here */

/* Main */

/* Core */
:root {
  --textColor: #616573;
  --textColorLight: #fff;
  --textColorDark: rgb(136, 137, 145);
  --textColorHeadlines: var(--accentColor);

  --textSize: 18px;
  --textLineHeight: 160%;

  --fontFamily: "CustomHeadingFont", sans-serif;
  --fontFamilySecond: "CustomContentFont";

  --arrowLightBlue: url("/wp-content/themes/moderaterna/img/arrow-lightblue.svg");
  --arrowBlue: url("/wp-content/themes/moderaterna/img/arrow-blue.svg");
  --arrowPink: url("/wp-content/themes/moderaterna/img/arrow-ping.svg");
  --arrowGrey: url("/wp-content/themes/moderaterna/img/arrow-grey.svg");

  --arrowDowm: url("/wp-content/themes/moderaterna/img/arrow-down.svg");

  --accentColor: #213a8f;
  --accentColor2: #93d5f6;
  --accentColor3: #f5b5d2;
  --accentColor4: #f8f8f8;

  --buttonColorLight: #fff;
  --buttonColorDark: var(--accentColor2);
  --buttonTextLight: var(--textColorLight);
  --buttonTextDark: var(--textColorLight);

  --mainLetterSpacing: 2px;
}
article {
  margin-bottom: 0px;
}
body {
  font-family: "Helvetica Neue";
}
p,
li {
  font-size: 18px;
  line-height: 1.7;
}

@media screen and (max-width: 768px) {
  p {
    font-size: 16px;
  }
}

a:hover {
  color: var(--accentColor2);
}
a:active {
  color: var(--accentColor) !important;
}
a {
  text-decoration: none;
}
html {
  scroll-behavior: smooth;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  text-transform: none;
  margin: 0px;
}

/* Header */

.smort-header {
  padding: 1% 2%;
  width: calc(100% - 4%);
}
.smort-nav ul li a {
  text-decoration: none;
  color: #000;
  font-size: 1.375rem;
  cursor: pointer;
}

.smort-nav ul li a {
  position: relative;
  text-decoration: none;
  color: inherit; /* Behåller textfärgen */
  padding-bottom: 5px; /* Avstånd för understrykningen */
  font-size: 20px;
  margin: 0px;
}
.smort-nav ul:not(.sub-menu) > li > a::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px; /* Tjockleken på linjen */
  background-color: currentColor; /* Använder textens färg */
  transition: width 0.3s ease-in-out;
}

.smort-nav ul li a:hover::after {
  width: 100%; /* Expanderar linjen vid hover */
}

a.bli-medlem-header.sticky {
  background-color: var(--accentColor2);
  color: var(--accentColor);
}

/* Footer */

.footer-link-col a {
  position: relative;
  text-decoration: none;
  color: #213a8f;
  padding-bottom: 5px; /* Avstånd för understrykningen */
  font-size: 14px;
  margin: 10px 0px;
}
.footer-link-col a::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px; /* Tjockleken på linjen */
  background-color: currentColor; /* Använder textens färg */
  transition: width 0.3s ease-in-out;
}

.footer-link-col a:hover::after {
  width: 100%; /* Expanderar linjen vid hover */
}

.footer-link-col p {
  margin: 0px;
}
.footer-link-col h2 {
  margin-bottom: 20px;
}
/* Buttons */

/* Dark blue btn */

.main-btn-moderaterna a {
  background-color: var(--accentColor);
  color: var(--accentColor2);
  border-radius: 8px;
  font-family: "CustomHeadingFont";
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  min-width: 180px;
}
.main-btn-moderaterna a:before {
  content: "";
  margin-right: 10px;
  width: 20px;
  height: 20px;
  display: inline-block;
  background-image: var(--arrowLightBlue);
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}
.main-btn-moderaterna a:hover {
  background-color: var(--accentColor3);
  color: var(--accentColor);
}

@media screen and (max-width: 768px) {
  .main-btn-moderaterna a {
    min-width: 150px;
  }
}

/* Header btn bli medlem */

a.bli-medlem-header {
  background-color: var(--accentColor);
  color: var(--accentColor2);
  border-radius: 8px;
  font-family: "CustomHeadingFont";
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  min-width: 150px;
  padding: 20px 25px;
}
a.bli-medlem-header:hover {
  background-color: var(--accentColor3);
  color: var(--accentColor);
}
/* Light btn */

.secondary-btn-moderaterna a {
  background-color: var(--accentColor2);
  color: var(--accentColor);
  border-radius: 8px;
  font-family: "CustomHeadingFont";
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  min-width: 180px;
}
.secondary-btn-moderaterna a:before {
  content: "";
  margin-right: 10px;
  width: 20px;
  height: 20px;
  display: inline-block;
  background-image: var(--arrowBlue);
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}
.secondary-btn-moderaterna a:hover {
  background-color: var(--accentColor3);
  color: var(--accentColor);
}

/* White btn */

.white-btn-moderaterna a {
  background-color: var(--accentColor4);
  color: var(--accentColor);
  border-radius: 8px;
  font-family: "CustomHeadingFont";
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  min-width: 180px;
}
.white-btn-moderaterna a:before {
  content: "";
  margin-right: 10px;
  width: 20px;
  height: 20px;
  display: inline-block;
  background-image: var(--arrowBlue);
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}

.white-btn-moderaterna a:hover {
  background-color: var(--accentColor2);
  color: var(--accentColor);
}

.wp-block-button__link {
  padding: 25px 20px;
}

@media screen and (max-width: 768px) {
  .wp-block-button__link {
    padding: 18px 20px;
  }
}

.wp-block-button__link:hover {
  transform: scale(0.95);
  transition-duration: 200ms;
}

/* Headings */

.hero-heading-moderaterna {
  font-size: 6rem;
  margin: 0px;
}

@media screen and (max-width: 768px) {
  .hero-heading-moderaterna {
    font-size: 3rem;
  }
}

.main-heading-moderaterna {
  font-size: 4rem;
  margin-bottom: 20px;
}

@media screen and (max-width: 768px) {
  .main-heading-moderaterna {
    font-size: 2.5rem;
  }
}

.sub-text-moderaterna {
  font-size: 25px;
  color: var(--accentColor);
}

.cta-heading-moderaterna {
  font-size: 3rem;
}

@media screen and (max-width: 768px) {
  .cta-heading-moderaterna {
    font-size: 2rem;
  }
}

.cta-heading-moderaterna:after {
  content: "";
  position: absolute;
  right: 25px;
  display: inline-block;
  width: 40px;
  height: 20px;
  background-image: var(--arrowGrey);
  background-size: contain;
  background-repeat: no-repeat;
  margin-top: 10px;
}

.cta-col-moderaterna:hover .cta-heading-moderaterna:after {
  transform: scale(1.3);
}
.cta-col-moderaterna:hover {
  transform: scale(0.98);
  animation-duration: 200ms;
}

.bli-medlem-heading {
  font-size: 2.5rem;
  margin: 10px;
}

/* General */

.hero-section-moderaterna::after {
  content: "";
  position: absolute;
  bottom: 50px; /* Justera så den är under diven */
  left: 50%;
  transform: translateX(-50%);
  width: 50px; /* Anpassa storleken efter behov */
  height: 50px;
  background-image: var(--arrowDowm);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: jumpAnimation 1.5s infinite ease-in-out;
}

@keyframes jumpAnimation {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(10px);
  }
}

/* Single program styling */

.hero-container-program {
  background-color: rgba(33, 58, 143, 0.8);
  height: 40vh;
  display: flex;
  justify-content: left;
  align-items: center;
  color: #ffff;
  padding: 0rem 10rem;
}
.hero-heading-program {
  font-size: 4rem;
  width: 80%;
}
.content-container-program {
  width: 90%;
  max-width: 800px;
  margin: auto;
  padding-top: 2rem;
}
.hero {
  background-size: cover;
  background-position: center;
}
@media screen and (max-width: 768px) {
  .content-container-program {
    width: 95%;
    /padding-top: 2rem;
  }
}
@media screen and (max-width: 1024px) {
  .hero-container-program {
    padding: 0rem 5rem;
  }
  .hero-heading-program {
    font-size: 2.5rem;
    width: 100%;
  }
}
@media screen and (max-width: 768px) {
  .hero-container-program {
    padding: 0rem 2rem;
  }
  .hero-heading-program {
    font-size: 2rem;
    width: 100%;
  }
}
.social-media-share-container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  margin-top: 4rem;
}
.social-media-share-container p {
  text-align: center;
  margin: 0;
}
.social-media-share {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 20px;
}
.social-media-share-icon {
  background-color: var(--accentColor);
  border-radius: 100%;
  padding: 10px 12px;
  margin: 0px 5px;
}
.social-media-share-icon img {
  width: 20px;
  height: 20px;
  filter: invert(1);
}

ul.wp-block-list li {
  line-height: 2;
}
.price-medlem {
  margin: 0px;
}

/* Våra politiker styling */

.hero-politiker {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  gap: 50px;
  align-items: center;
  padding-top: 4%;
}
img.politiker-img {
  width: 300px;
  height: 300px;
  object-fit: cover;
  object-position: center top;
  border-radius: 50%;
}
p.utrdrag-politiker {
  margin-top: 10px;
}
a.politiker-mail-symbol img {
  background-color: var(--accentColor);
  padding: 10px;
  border-radius: 50%;
}
a.politiker-link-symbol img{
  background-color: var(--accentColor);
  padding: 10px;
  border-radius: 50%;

}
.content-politiker-program {
  max-width: 800px;
  margin: 0 auto;
  padding-bottom: 5%;
  padding-top: 2rem;
}
.outer-telefon-section {
  background-color: var(--accentColor3);
  padding: 4%;
}
.telefon-section {
  max-width: 800px;
  margin: 0 auto;
}
span.telefon-politiker-label {
  font-family: "CustomHeadingFont";
  line-height: 2;
}
a.telefon-politiker-number {
  color: #000;
}

@media screen and (max-width: 768px) {
  .hero-politiker,
  .utrdrag-politiker {
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding: 0 1rem;
    gap: 0px;
    text-align: center;
  }

  .hero-politiker h1 {
    padding-top: 1rem;
  }

  .content-politiker-program {
    padding: 0 1rem;
  }

  .outer-telefon-section {
    text-align: center;
  }
}

/* Instagram feed */

#sb_instagram img {
  border-radius: 8px;
}
