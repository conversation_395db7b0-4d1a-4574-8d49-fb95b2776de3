body,
html {
  margin: 0;
  padding: 0;
}

.smort-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 1% 2%;
}

.smort-logo img {
  display: block;
  margin: 0 auto;
}

.smort-nav {
  text-align: center;
}

.smort-nav.smort-nav-centered {
  width: 60%;
  justify-content: center;
}

.smort-nav.smort-nav-left {
  width: 90%;
  justify-content: flex-start;
}

.smort-nav ul:not(.sub-menu) {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
}

.smort-nav ul li {
  margin: 0 15px;
}

.smort-nav ul li a {
  text-decoration: none;
  color: #000;
}

.smort-nav ul > li > a:hover,
.smort-nav ul > li > a:active {
  color: var(--accentColor) important;
}
.smort-nav ul li ul li a:hover, 
.smort-nav ul li ul li a:active {
  color: var(--accentColor3) !important;
} 

.smort-cart {
  width: 25%;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.smort-header-style_2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.smort-header-style_2 .smort-nav {
  width: 40%;
  text-align: left;
}

.smort-header-style_2 .smort-logo {
  width: 20%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.smort-header-style_2 .smort-cart {
  width: 40%;
  text-align: right;
}

.smort-header.transparent {
  background: none;
  position: absolute;
  width: 100%;
}

body.transparent-header {
  margin-top: 0;
}
/* Add this to style.css */

/* Burger Menu */
.burger-menu {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
  width: 30%;
}

.burger-menu span {
  background: #000;
  height: 2px;
  width: 100%;
  margin: 4px 0;
}f
.sub-menu-xk {
  display: flex;
  flex-direction: row;
  width: 100%;
  width: 90%;
  overflow-y: auto;
  text-align: left;
  padding-bottom: 10%;
  border-top: 1px solid #f1f1f1;
  padding-top: 5%;
  gap: 40px;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}
.sub-menu-xk a {
  color: #fff;
  font-size: 2rem;
}

.mobile-nav-inner {
  text-align: center;
  width: 100%;
}

.close-nav {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  font-size: 30px;
  color: #fff;
  cursor: pointer;
  z-index: 99;
}

.sub-menu {
  display: none;
  width: 100%;
  text-align: left;
  background-color: var(--accentColor);
  padding-left:0;
  border-radius: 4px;
  opacity:0;
  transition: opacity 0.3s ease;
  position: absolute;
  z-index: 9999;
  max-width:fit-content;
  padding: 10px 5px !important;
  li a {
    color: var(--accentColor2)!important;
  }
  li a:hover {
    color: var(--accentColor2) !important;
  }
}

.smort-nav ul:not(.sub-menu) li:has(.sub-menu) {
  & > a {
    display: flex;
  } 
  & > a::after{
    position:relative;
    display: inline-block;
    content: url(img/menu-down-smort.svg);
    vertical-align: middle;
    margin-left: 4px;
    width: 16px;
    height: 16px;
    font-size: 1.5rem;
    background-color: transparent;
    transition: transform 0.3s ease;
  }
}

.smort-nav li:has(ul.sub-menu):hover {
  .sub-menu {
    display: block;
    opacity:1;
    li {
        list-style: none;
    }
  }
}

@media (min-width: 992px) {
  .burger-menu-container {
    display: none !important;
  }
}

@media (max-width: 992px) {
  .burger-menu {
    display: flex;
  }

  .main-navigation {
    display: none;
  }
}
