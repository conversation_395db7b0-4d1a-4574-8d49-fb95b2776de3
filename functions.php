<?php
// functions.php

function enqueue_custom_styles_scripts() {
    wp_enqueue_style('child-theme-css', get_stylesheet_directory_uri() . '/style.css', array(), '1.0', 'all');
    wp_enqueue_style('header-css', get_stylesheet_directory_uri() . '/header.css', array(), '1.0', 'all', 10);
    wp_enqueue_style('program-loop-css', get_stylesheet_directory_uri() . '/template-parts/smortblocks/program-loop/program-loop.css', array(), '1.0', 'all', 10);
    
}
add_action('wp_enqueue_scripts', 'enqueue_custom_styles_scripts', 20);

/* Enque admin styling */ 

function mitt_enqueue_admin_style() {
    wp_enqueue_style( 'mitt-admin-style', get_stylesheet_directory_uri() . '/style.css' );
}
add_action( 'admin_enqueue_scripts', 'mitt_enqueue_admin_style' );

/* --- */ 

function register_acf_block_types() {
	
	// Logos scroll Smort Hero block
	acf_register_block_type(array(
		'name'				    => 'Smort Kalender',
		'title'			 	    => __('Smort - Kalender'),
		'description'		  => __('Smort block'),
		'render_template'	=> '/template-parts/smortblocks/smort-kalender/smort-kalender.php',
		'category'			  => 'smort',
		'icon'				    => 'id',
		'keywords'			  => array( 'CTA', 'Block' ),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-kalender/smort-kalender.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-kalender/smort-kalender.js',
	));
    acf_register_block_type(array(
        'name'                    => 'program',
        'title'                     => __('Program loop'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/program-loop/program-loop.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
    ));
    acf_register_block_type(array(
        'name'                    => 'nyheter',
        'title'                     => __('Nyheter grid'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/nyheter-grid/nyheter-grid.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/nyheter-grid/nyheter-grid.css',
    ));
    acf_register_block_type(array(
        'name'                    => 'politiker',
        'title'                     => __('Politiker grid'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/politiker-grid/politiker-grid.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/politiker-grid/politiker-grid.css',
    ));
}
// Check if function exists and hook into setup.
if( function_exists('acf_register_block_type') ) {
    add_action('acf/init', 'register_acf_block_types');
  }


  function enqueue_swiper_slider() {
    // Enqueue Swiper CSS
    wp_enqueue_style('swiper-css', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css', array(), '10.0.0');

    // Enqueue Swiper JS
    wp_enqueue_script('swiper-js', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js', array(), '10.0.0', true);

    
}
add_action('wp_enqueue_scripts', 'enqueue_swiper_slider');


function theme_setup()
{
    add_theme_support('post-thumbnails');
}
add_action('after_setup_theme', 'theme_setup');

function program_post_type()
{
    register_post_type(
        'program',
        array(
            'labels' => array(
                'name' => __('Program'),
                'singular_name' => __('Program')
            ),
            'public' => true,
            'show_in_rest' => true,
            'supports' => array('title', 'editor', 'thumbnail'),
            'menu_icon' => 'dashicons-welcome-add-page'
        )
    );
    register_post_type(
        'politiker',
        array(
            'labels' => array(
                'name' => __('Våra politiker'),
                'singular_name' => __('Våra politiker')
            ),
            'public' => true,
            'show_in_rest' => true,
            'supports' => array('title', 'editor', 'thumbnail'),
            'menu_icon' => 'dashicons-welcome-add-page'
        )
    );
    
}
add_action('init', 'program_post_type');

