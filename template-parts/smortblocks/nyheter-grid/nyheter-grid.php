<?php


$args = array(
    'posts_per_page' => get_field('limit'),
    'orderby' => 'date',
    'order' => 'DESC',
);

$program_query = new WP_Query($args);
$kolumner = get_field('kolumner');
$limit = get_field('limit');

if ($program_query->have_posts()) : ?>
    <div class="program-list nyheter" style="grid-template-columns: repeat(<?php echo $kolumner; ?>  , 1fr);">
        <?php while ($program_query->have_posts()) : $program_query->the_post(); ?>
            <div class="program-item">
                <a href="<?php the_permalink(); ?>">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="program-thumbnail">
                            <?php the_post_thumbnail('large'); ?>
                        </div>
                    <?php endif; ?>
                    <div class="program-info-div">
                        <span class="post-date"><?php the_date(); ?></span>
                        <h2 class="program-title"><?php the_title(); ?></h2>
                        <a class="las-mer-program" href="<?php the_permalink(); ?>">Läs mer</a>
                    </div>
                </a>
            </div>
        <?php endwhile; ?>
    </div>
<?php else : ?>
    <p>Inga nyheter hittades.</p>
<?php endif;
wp_reset_postdata();
?>