<?php

$args = array(
    'post_type' => 'politiker',
    'posts_per_page' => get_field('limit'),
    'orderby' => 'date',
    'order' => 'DESC',
);

global $post;

$politikerquery = new WP_Query($args);
$kolumner = get_field('kolumner');
$limit = get_field('limit');

if ($politikerquery->have_posts()) : ?>
    <div class="politiker-container">
        <div class="program-list nyheter" style="grid-template-columns: repeat(<?php echo $kolumner; ?>, 1fr);">
            <?php while ($politikerquery->have_posts()) : $politikerquery->the_post(); ?>
                <div class="politiker-item">
                    <?php if (!empty(trim(apply_filters('the_content', $post->post_content)))) : ?>
                        <a href="<?php the_permalink(); ?>" class="politiker-link">
                        <?php endif; ?>
                        <div class="politiker-info-div">
                            <div class="politiker-thumbnail">
                                <img src="<?php echo get_field('bild', $post); ?>" class="img-politiker">
                                <h3 class="politiker-title"><?php the_title(); ?></h3>
                                <p class="roll-politiker"><?php echo get_field('roll', $post); ?></p>
                                <?php if (!empty(trim(apply_filters('the_content', $post->post_content)))) : ?>
                                    <a class="cta-politiker-grid" href="<?php the_permalink(); ?>">Läs mer →</a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if (!empty(trim(apply_filters('the_content', $post->post_content)))) : ?>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endwhile; ?>
        </div>
    </div>
<?php else : ?>
    <p>Inga politiker hittades.</p>
<?php endif;
wp_reset_postdata();
?>