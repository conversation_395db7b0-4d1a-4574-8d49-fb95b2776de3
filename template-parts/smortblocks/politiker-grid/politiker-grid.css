.politiker-thumbnail img {
  height: 250px;
  width: 250px;
  border-radius: 650%;
  object-fit: cover;
  object-position: center top;
}
.politiker-info-div {
  display: flex;
  justify-content: center;
  text-align: center;
  color: #000;
}
h3.politiker-title {
  font-size: 1.5rem;
  margin-top: 15px;
}
p.roll-politiker {
  margin-top: 10px;
  font-size: 14px;
}
.politiker-info-div {
  color: #000;
}
.politiker-thumbnail * {
  color: #000;
}
.politiker-thumbnail a {
  color: var(--accentColor);
}
.program-list.nyheter {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  row-gap: 50px;
}

@media (max-width: 1500px) {
  .program-list.nyheter {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 10px;
    row-gap: 40px;
  }
}

@media (max-width: 1000px) {
  .program-list.nyheter {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 10px;
    row-gap: 40px;
  }
}

@media (max-width: 480px) {
  .program-list.nyheter {
    grid-template-columns: 1fr !important;
    gap: 10px;
    row-gap: 40px;
  }
}

.politiker-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 15px;
  box-sizing: border-box;
}
