<?php
$args = array(
    'post_type' => 'program',
    'posts_per_page' => -1,
    'orderby' => 'date',
    'order' => 'DESC',
);
$program_query = new WP_Query($args);
if ($program_query->have_posts()) : ?>
    <div class="program-list">
        <?php while ($program_query->have_posts()) : $program_query->the_post(); ?>
            <div class="program-item">
                <a href="<?php the_permalink(); ?>">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="program-thumbnail">
                            <?php the_post_thumbnail('large'); ?>
                        </div>
                    <?php endif; ?>
                    <div class="program-info-div">
                        <h2 class="program-title"><?php the_title(); ?></h2>
                        <a class="las-mer-program" href="<?php the_permalink(); ?>">L<PERSON>s mer</a>
                    </div>
                </a>
            </div>
        <?php endwhile; ?>
    </div>
<?php else : ?>
    <p>No programs found.</p>
<?php endif;
wp_reset_postdata();
?>