.program-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}
.program-item {
  box-sizing: border-box;
  padding: 0px;
  background-color: var(--accentColor4);
  border-radius: 8px;
}
.program-thumbnail {
  position: relative;
  width: 100%;
  padding-bottom: 80%;
  overflow: hidden;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.program-thumbnail img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}
.program-thumbnail::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.5s ease;
  z-index: 1;
}
.program-thumbnail:hover::before {
  background-color: rgba(0, 0, 0, 0.2);
}
.program-title {
  font-size: 1.5rem;
  margin-top: 1rem;
  margin-bottom: 0;
  color:  var(--accentColor);
  min-height: 60px;
}
.program-info-div {
  padding: 10px;
}
a.las-mer-program {
  background-color: var(--accentColor);
  color: var(--accentColor2);
  border-radius: 8px;
  font-family: 'CustomHeadingFont';
  display: flex
;
  justify-content: center;
  align-items: center;
  line-height: 1;
  padding: 15px 20px;
  margin-top: 15px;
  display: block;
  max-width: 200px;
  text-align: center;
}
h2.related-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 30px;
  color: var(--accentColor);
}

@media screen and (max-width: 1024px) {
  .program-list {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 768px) {
  .program-list {
    grid-template-columns: 1fr;
  }
}
.content-container-relatad-program {
  width: 90%;
  max-width: 1200px;
  margin: auto;
  padding-top: 3rem;
  padding-bottom: 3rem;
}
@media screen and (max-width: 768px) {
  .content-container-relatad-program {
    width: 95%;
    padding-top: 2rem;
  }
}