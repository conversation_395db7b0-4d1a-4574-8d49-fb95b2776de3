.week-container {
  font-family: Arial, sans-serif;
}

.week-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.week-navigation button {
  cursor: pointer;
  border-radius: 8px;
  background-color: var(--accentColor2);
  border: 0px;
  font-family: "CustomHeadingFont";
  color: var(--accentColor);
  padding: 10px 8px;
}

#current-week {
  font-weight: bold;
  font-size: 1.7em;
  color: var(--accentColor2);
}

@media (max-width: 768px) {
  #current-week {
    font-weight: bold;
    font-size: 20px;
    color: var(--accentColor2);
  }
}

.day-events {
  margin-bottom: 20px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
  background-color: var(--accentColor2);
  padding: 15px;
  border-radius: 8px;
  color: var(--accentColor);
}

.day-events h3 {
  margin: 0 0 10px;
  font-size: 2em;
}

.events-list {
  list-style: none;
  padding-left: 0;
}

.event {
  margin: 15px 0;
  padding: 10px 0px 15px;
  font-size: 20px;
  font-family: "CustomHeadingFont";
  border-bottom: 1px solid;
  line-height: 2;
}

@media (max-width: 768px) {
  .event {
    display: flex;
    flex-direction: column;
  }
}

p.no-events-message {
  background-color: var(--accentColor2);
  border-radius: 8px;
  padding: 10px;
  color: var(--accentColor);
  font-family: "CustomHeadingFont";
  margin-top: 3rem;
}
button.details-button {
  float: right;
  background-color: var(--accentColor);
  color: var(--accentColor2);
  min-width: 150px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding: 10px 0px;
  border-radius: 8px;
  font-family: "CustomHeadingFont";
  border: 0px;
}

@media (max-width: 768px) {
  button.details-button {
    margin-top: 0.5rem;
  }
}

.event-description {
  font-size: 17px;
  font-family: "CustomContentFont";
  margin-top: 1%;
}

@media (max-width: 768px) {
  .event-description {
    margin-top: 1.5rem;
  }
}
.week-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 10px;
}

.week-navigation button {
  cursor: pointer;
  border-radius: 8px;
  background-color: var(--accentColor2);
  border: 0px;
  font-family: "CustomHeadingFont";
  color: var(--accentColor);
  padding: 10px 8px;
  flex: 1;
  max-width: 150px;
}

#next-event {
  background-color: var(--accentColor);
  color: var(--accentColor2);
}