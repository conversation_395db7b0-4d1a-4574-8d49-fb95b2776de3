(function(){
    // Använd kalenderdatan som skickas från PHP via inline-script
    const eventsData = calendarData.events || [];
  
    // Hjälpfunktion: Beräknar veckonummer enligt ISO‑standard
    function getWeekNumber(d) {
        d = new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()));
        d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay() || 7));
        const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
        const weekNo = Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
        return weekNo;
    }

    // Hjälpfunktion: Formaterar tid (t.ex. "16:00")
    function formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString('sv-SE', { hour: '2-digit', minute: '2-digit' });
    }
  
    // Renderar händelserna för den angivna veckan
    function renderWeekEvents(weekNumber, eventsData) {
        const weekEventsContainer = document.getElementById('week-events');
        if (!weekEventsContainer) return;
        weekEventsContainer.innerHTML = ''; // Rensa tidigare innehåll
  
        // Filtrera ut händelser som tillhör angiven vecka
        const filteredEvents = eventsData.filter(event => {
            const eventDate = new Date(event.start);
            return getWeekNumber(eventDate) === weekNumber;
        });
  
        // Om inga händelser hittas, visa ett meddelande
        if (filteredEvents.length === 0) {
            const noEventsMsg = document.createElement('p');
            noEventsMsg.className = 'no-events-message';
            noEventsMsg.textContent = 'Inga händelser inplanerade denna vecka';
            weekEventsContainer.appendChild(noEventsMsg);
            return;
        }
  
        // Gruppindela händelser per dag (endast dagar med händelser)
        const eventsByDay = {};
        filteredEvents.forEach(event => {
            const eventDate = new Date(event.start);
            const dayString = eventDate.toLocaleDateString('sv-SE', { day: 'numeric', month: 'long' });
            if (!eventsByDay[dayString]) {
                eventsByDay[dayString] = [];
            }
            eventsByDay[dayString].push(event);
        });
  
        // Rendera varje dag med händelser
        for (const day in eventsByDay) {
            const dayDiv = document.createElement('div');
            dayDiv.className = 'day-events';
  
            const dayHeader = document.createElement('h3');
            dayHeader.textContent = day;
            dayDiv.appendChild(dayHeader);
  
            const ul = document.createElement('ul');
            ul.className = 'events-list';
  
            eventsByDay[day].forEach(event => {
                const li = document.createElement('li');
                li.className = 'event';
                
                // Skapa en span med grundinfo
                const eventInfo = document.createElement('span');
                eventInfo.textContent = `${event.title} - ${formatTime(event.start)} - ${formatTime(event.end)}`;
                li.appendChild(eventInfo);
  
                // Kontrollera om det finns en beskrivning med innehåll
                if (!event.description || event.description.trim() === '') {
                    // Ingen beskrivning – visa en inaktiv knapp
                    const detailsBtn = document.createElement('button');
                    detailsBtn.className = 'details-button disabled';
                    detailsBtn.textContent = 'Inga detaljer angivna';
                    detailsBtn.disabled = true;
                    li.appendChild(detailsBtn);
                } else {
                    // Det finns en beskrivning – skapa klickbar knapp
                    const detailsBtn = document.createElement('button');
                    detailsBtn.className = 'details-button';
                    detailsBtn.textContent = 'Detaljer';
                    li.appendChild(detailsBtn);
  
                    // Skapa en dold div för beskrivningen.
                    const descriptionDiv = document.createElement('div');
                    descriptionDiv.className = 'event-description';
                    descriptionDiv.style.display = 'none';
                    // Ersätt newline-tecken med <br> och byt ut "\," mot ett vanligt komma
                    descriptionDiv.innerHTML = event.description
                        .replace(/\\n|\n/g, '<br>')
                        .replace(/\\,/g, ',');
                    li.appendChild(descriptionDiv);
  
                    // Lägg till klick-händelse för att toggla visning av beskrivningen
                    detailsBtn.addEventListener('click', function() {
                        if (descriptionDiv.style.display === 'none') {
                            descriptionDiv.style.display = 'block';
                        } else {
                            descriptionDiv.style.display = 'none';
                        }
                    });
                }
                
                ul.appendChild(li);
            });
  
            dayDiv.appendChild(ul);
            weekEventsContainer.appendChild(dayDiv);
        }
    }
  
    // Bestäm aktuellt veckonummer och rendera initialt
    let currentWeek = getWeekNumber(new Date());
    const currentWeekSpan = document.getElementById('current-week');
    if (currentWeekSpan) {
        currentWeekSpan.textContent = 'Vecka ' + currentWeek;
    }
  
    renderWeekEvents(currentWeek, eventsData);
  
    // Hantera navigering
    const prevWeekBtn = document.getElementById('prev-week');
    const nextWeekBtn = document.getElementById('next-week');
      const nextEventBtn = document.getElementById('next-event');

    if (nextEventBtn) {
        nextEventBtn.addEventListener('click', function() {
            const nextEvent = findNextEvent(eventsData);
            console.log('nextEvent', nextEvent);
            if (nextEvent) {
                const eventDate = new Date(nextEvent.start);
                const eventWeek = getWeekNumber(eventDate);
                currentWeek = eventWeek;
                if (currentWeekSpan) currentWeekSpan.textContent = 'Vecka ' + currentWeek;
                renderWeekEvents(currentWeek, eventsData);
            }
        });
    }
    if (prevWeekBtn) {
        prevWeekBtn.addEventListener('click', function() {
            currentWeek--;
            if (currentWeekSpan) currentWeekSpan.textContent = 'Vecka ' + currentWeek;
            renderWeekEvents(currentWeek, eventsData);
        });
    }
  
    if (nextWeekBtn) {
        nextWeekBtn.addEventListener('click', function() {
            currentWeek++;
            if (currentWeekSpan) currentWeekSpan.textContent = 'Vecka ' + currentWeek;
            renderWeekEvents(currentWeek, eventsData);
        });
    }
    // Hjälpfunktion: Hitta nästa händelse från dagens datum
    function findNextEvent(eventsData) {
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Sätt till början av dagen
        
        const upcomingEvents = eventsData.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate >= today;
        });
        
        if (upcomingEvents.length === 0) return null;
        
        // Sortera händelser efter datum och returnera den första
        upcomingEvents.sort((a, b) => new Date(a.start) - new Date(b.start));
        return upcomingEvents[0];
    }

})();
