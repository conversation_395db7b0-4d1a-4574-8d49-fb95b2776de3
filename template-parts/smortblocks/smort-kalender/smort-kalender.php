<?php

/**
 * <PERSON><PERSON><PERSON> <PERSON><PERSON> block render template.
 */

// --------------------------
// Hämta och parsa kalenderdata
// --------------------------

/**
 * Enkel ICS-parser – OBS! Denna parser är förenklad och hanterar kanske inte alla ICS-format.
 */
if (!function_exists('parse_ics_to_array')) {
    function parse_ics_to_array($ics_content)
    {
        // Unfold ICS-rader enligt specifikationen: ta bort CRLF följt av mellanslag eller tab.
        $ics_content = preg_replace("/\r\n[ \t]/", '', $ics_content);

        $lines = preg_split("/\r\n|\n|\r/", $ics_content);
        $events = array();
        $event = null;

        foreach ($lines as $line) {
            if (strpos($line, 'BEGIN:VEVENT') !== false) {
                $event = array();
            } elseif (strpos($line, 'END:VEVENT') !== false) {
                if ($event) {
                    if (isset($event['SUMMARY']) && isset($event['DTSTART']) && isset($event['DTEND'])) {
                        $events[] = array(
                            'title'       => $event['SUMMARY'],
                            'start'       => format_ics_date($event['DTSTART']),
                            'end'         => format_ics_date($event['DTEND']),
                            'description' => isset($event['DESCRIPTION']) ? $event['DESCRIPTION'] : ''
                        );
                    }
                    $event = null;
                }
            } elseif ($event !== null) {
                $parts = explode(":", $line, 2);
                if (count($parts) == 2) {
                    $key = strtoupper(trim($parts[0]));
                    $value = trim($parts[1]);
                    // Om nyckeln innehåller parametrar, använd endast själva nyckeln
                    $keyParts = explode(";", $key);
                    $key = $keyParts[0];
                    $event[$key] = $value;
                }
            }
        }
        return $events;
    }
}

/**
 * Hjälpfunktion för att formatera ICS-datum till ISO‑8601-format (Y-m-d\TH:i:s)
 */
if (!function_exists('format_ics_date')) {
    function format_ics_date($dateString)
    {
        if (strpos($dateString, 'T') !== false) {
            if (substr($dateString, -1) == 'Z') {
                $dateTime = DateTime::createFromFormat('Ymd\THis\Z', $dateString, new DateTimeZone('UTC'));
                // Konvertera till önskad tidszon, t.ex. Europe/Stockholm
                $dateTime->setTimezone(new DateTimeZone('Europe/Stockholm'));
            } else {
                $dateTime = DateTime::createFromFormat('Ymd\THis', $dateString);
            }
        } else {
            $dateTime = DateTime::createFromFormat('Ymd', $dateString);
        }
        return $dateTime ? $dateTime->format('Y-m-d\TH:i:s') : $dateString;
    }
}

/**
 * Hämtar kalenderhändelser från din publika ICS-URL.
 */
if (!function_exists('my_load_calendar_events')) {
    function my_load_calendar_events()
    {
        // Ange din publika ICS-länk från Google Kalender.
        $ics_url = get_field('kalender_url');

        $response = wp_remote_get($ics_url);
        if (is_wp_error($response)) {
            return array();
        }
        $ics_content = wp_remote_retrieve_body($response);
        $events = parse_ics_to_array($ics_content);
        return $events;
    }
}

$events = my_load_calendar_events();
?>

<!-- Skriv ut inline script med kalenderdata (så att calendarData definieras innan JS-koden körs) -->
<script type="text/javascript">
    var calendarData = {
        events: <?php echo wp_json_encode($events); ?>
    };
    console.log('🟡 ~ calendarData', calendarData);
</script>

<div class="week-container">
    <div class="week-navigation">
        <button id="prev-week">Föregående vecka</button>
        <button id="next-event">Nästa händelse</button>
        <span id="current-week">Vecka 12</span>
        <button id="next-week">Nästa vecka</button>
    </div>
    <div id="week-events">
        <!-- Händelser renderas här av JavaScript -->
    </div>
</div>